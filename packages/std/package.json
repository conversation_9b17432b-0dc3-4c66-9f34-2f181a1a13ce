{"type": "module", "version": "0.0.5", "name": "@ozaco/std", "bundleDependencies": ["picocolors", "type-fest"], "dependencies": {"type-fest": "^4.41.0"}, "devDependencies": {"@ozaco/cli": "workspace:*"}, "exports": {"./result": {"default": "./dist/result.js", "source": "./src/result/index.ts", "types": "./dist/result.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "author": "giveerr (https://github.com/giveerr)", "homepage": "https://ozaco.com/", "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}}