import type { BlobType } from '../../shared'

import { err } from './result'

export const fn = <Args extends BlobType[], Return extends BlobType, const Causes extends string[]>(
  cb: (...args: Args) => Return,
  ...causes: Causes
) => {
  return (
    ...args: Args
  ): std.Result<
    std.ExtractOk<Return>,
    std.ExtractName<Return> | 'internal',
    std.ExtractCauses<Return>[number] extends never ? [] : std.ExtractCauses<Return>[number][]
  > => {
    try {
      return cb(...args) as BlobType
    } catch (rawError) {
      return err('internal', 'An internal error occurred', ...causes) as BlobType
    }
  }
}
