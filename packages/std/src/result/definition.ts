import type { BlobType, UnionToTuple } from '../shared'

import { fn } from './utils/fn'
import { err, errSym, isErr, isOk, ok, okSym } from './utils/result'

declare global {
  export var std: {
    result: {
      ok: typeof ok
      err: typeof err
      isOk: typeof isOk
      isErr: typeof isErr
      okSym: typeof okSym
      errSym: typeof errSym
      fn: typeof fn
    }
  }

  export namespace std {
    export type Ok<Type> = {
      [okSym]: Type
    }

    export type Err<Name extends string, Causes extends string[]> = {
      [errSym]: [Name, string, Causes]
    }

    export type Result<Type, Name extends string, Causes extends string[]> = Ok<Type> | Err<Name, Causes>

    export type ExtractOk<Type> = Type extends Ok<infer Type2>
      ? Type2
      : Type extends Err<BlobType, BlobType>
        ? never
        : Type
    export type ExtractName<Type> = Type extends Err<infer Name, BlobType> ? Name : never
    export type ExtractCauses<Type> = Type extends Err<BlobType, infer Causes> ? UnionToTuple<Causes[number]> : never
  }
}

if (!globalThis.std) {
  globalThis.std = Object.assign(globalThis.std ?? {}, {
    result: Object.assign((globalThis.std as BlobType)?.result ?? {}, {
      err,
      errSym,
      fn,
      isErr,
      isOk,
      ok,
      okSym,
    }),
  })
}
